<!DOCTYPE html>
<html>
<head>
    <title>TipTap Extensions Test</title>
</head>
<body>
    <h1>TipTap Extensions Debug Test</h1>
    
    <h2>Test Commands:</h2>
    <p>Open browser console and try these commands:</p>
    
    <pre>
// Test if editor is available
console.log('Editor:', window.editor);

// Test equation command
window.editor?.commands.setEquationBlock({ latex: 'E = mc^2' });

// Test theorem command  
window.editor?.commands.setTheoremBlock({ type: 'theorem', title: 'Test Theorem' });

// Check available commands
console.log('Available commands:', Object.keys(window.editor?.commands || {}));

// Check if extensions are loaded
console.log('Extensions:', window.editor?.extensionManager?.extensions?.map(ext => ext.name));
    </pre>
    
    <h2>Expected Behavior:</h2>
    <ul>
        <li>setEquationBlock should insert a math equation block</li>
        <li>setTheoremBlock should insert a theorem block</li>
        <li>Both commands should be available in the commands object</li>
        <li>Extensions should include 'equationBlock' and 'theoremBlock'</li>
    </ul>
    
    <h2>Troubleshooting:</h2>
    <ul>
        <li>Check if extensions are properly imported in TipTapEditor.jsx</li>
        <li>Verify that commands are correctly defined in AcademicNodes.js</li>
        <li>Ensure CSS styles are loaded for proper display</li>
        <li>Check browser console for any JavaScript errors</li>
    </ul>
</body>
</html>
