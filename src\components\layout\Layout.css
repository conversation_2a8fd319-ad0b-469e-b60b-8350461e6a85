.layout-container {
  min-height: 100dvh;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
  /* 确保容器能够扩展以适应内容 */
  background: #40B5F7;
}

.layout-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  z-index: 1;
  transition: background-color 0.3s ease;
  background: #40B5F7
}

.layout-background-img {
  position: fixed;
  bottom: 0;
  left: 0;
  top: 0;
  width: 100%;
  height: auto;
  /* 增加图片高度，确保与渐变有更好的重叠 */
  object-fit: cover;
  object-position: bottom center;
  z-index: 1;
  /* 确保图片在最上层 */
  pointer-events: none;
  /* 确保图片不会阻挡用户交互 */
  /* 确保图片从底部中心开始显示 */
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  /* Account for fixed header */
  min-height: calc(100vh - 13vh);
  justify-content: center;
  align-items: center;
}

.main-content {
  width: 100%;
  max-width: 100%;
  /* margin: 2rem auto; */
  position: relative;
  z-index: 1;
}

.layout-container::webkit-scrollbar {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .layout-main {
    padding-top: 80px;
    /* Smaller header on mobile */
    min-height: calc(100vh - 80px);
  }
}

/* Ensure content doesn't get hidden behind header */
.layout-main::before {
  content: '';
  display: block;
  height: 20px;
  /* Additional spacing */
}



/* Content area styling */
/* .main-content {
  padding: 0 16px 32px;
}

@media (min-width: 768px) {
  .main-content {
    padding: 0 32px 48px;
  }
}

@media (min-width: 1200px) {
  .main-content {
    padding: 0 48px 64px;
  }
} */

/* Light theme styles */
.light-theme {
  color: #333;
}


@media (max-width: 768px) {
  .layout-main {
    padding-top: 80px;
    /* Smaller header on mobile */
    min-height: calc(100vh - 80px);
  }
}
