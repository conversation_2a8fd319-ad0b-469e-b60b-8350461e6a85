const statWidget = {
    // Metadata
    metadata: {
        name: 'Total Papers Widget',
        version: '0.1.9',
        description: 'Floating widget showing total paper count (always visible, English)'
    },

    // HTML template
    html: [
        '<div class="stat-widget">',
        '    <div class="stat-container stat-always">',
        '        <div class="stat-header">',
        '            <h3>Total Papers</h3>',
        '        </div>',
        '        <div class="stat-content" id="statContent">',
        '            <div class="stat-number" id="totalPapers">--</div>',
        '        </div>',
        '        <div class="stat-footer">',
        '            <div class="last-updated" id="lastUpdated">Last updated: --</div>',
        '        </div>',
        '    </div>',
        '</div>'
    ].join('\n'),

    // CSS styles (with mobile hide)
    css: [
        '.stat-widget {',
        '    position: fixed;',
        '    right: 30px;',
        '    bottom: 250px;',
        '    z-index: 1000;',
        '}',
        '',
        '.stat-container.stat-always {',
        '    width: 200px;',
        '    max-width: 90vw;',
        '    background: white;',
        '    border-radius: 16px;',
        '    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);',
        '    overflow: hidden;',
        '    display: block;',
        '}',
        '',
        '.stat-header {',
        '    padding: 15px;',
        '    background: #f8f9fa;',
        '    border-bottom: 1px solid #e2e8f0;',
        '    text-align: center;',
        '}',
        '',
        '.stat-header h3 {',
        '    margin: 0;',
        '    font-size: 15px;',
        '    color: #2d3748;',
        '}',
        '',
        '.stat-content {',
        '    padding: 18px 0;',
        '    background: #fff;',
        '    text-align: center;',
        '}',
        '',
        '.stat-number {',
        '    font-size: 1.7rem;',
        '    font-weight: 700;',
        '    color: #6366f1;',
        '    margin-bottom: 8px;',
        '    line-height: 1;',
        '}',
        '',
        '.stat-footer {',
        '    padding: 8px 12px;',
        '    font-size: 0.8rem;',
        '    color: #4a5568;',
        '    background: #f8f9fa;',
        '    border-top: 1px solid #e2e8f0;',
        '    text-align: right;',
        '}',
        '',
        '@media (max-width: 600px) {',
        '    .stat-widget {',
        '        display: none !important;',
        '    }',
        '}',
        '@media (max-width: 600px) {',
        '    .stat-widget {',
        '        right: 4vw;',
        '        bottom: 120px;',
        '    }',
        '    .stat-container.stat-always {',
        '        width: 90vw;',
        '        min-width: 0;',
        '        left: 5vw;',
        '        right: 5vw;',
        '        border-radius: 12px;',
        '    }',
        '    .stat-header, .stat-footer {',
        '        padding-left: 8px;',
        '        padding-right: 8px;',
        '    }',
        '    .stat-header h3 {',
        '        font-size: 14px;',
        '    }',
        '    .stat-number {',
        '        font-size: 1.2rem;',
        '    }',
        '}',
        '@media (max-width: 400px) {',
        '    .stat-widget {',
        '        right: 2vw;',
        '        bottom: 80px;',
        '    }',
        '    .stat-container.stat-always {',
        '        width: 98vw;',
        '        left: 1vw;',
        '        right: 1vw;',
        '        border-radius: 8px;',
        '    }',
        '    .stat-header h3 {',
        '        font-size: 13px;',
        '    }',
        '    .stat-number {',
        '        font-size: 1rem;',
        '    }',
        '    .stat-footer {',
        '        font-size: 0.7rem;',
        '    }',
        '}',
        '',
        '.error-message {',
        '    padding: 12px;',
        '    margin: 12px 0;',
        '    background: rgba(239, 68, 68, 0.1);',
        '    border: 1px solid rgba(239, 68, 68, 0.3);',
        '    border-radius: 6px;',
        '    color: #ef4444;',
        '    font-size: 0.875rem;',
        '    text-align: center;',
        '}'
    ].join('\n'),

    // JavaScript logic
    js: [
        '// Floating stat widget logic (always visible, English comments)',
        'async function fetchStatsFile() {',
        '    // Query the latest statistics transaction',
        '    const query = `',
        '        query {',
        '            transactions(',
        '                tags: [',
        '                    { name: "App-Name", values: ["scivault"] },',
        '                    { name: "Content-Type", values: ["application/json"] },',
        '                    { name: "Version", values: ["2.0.0"] },',
        '                    { name: "type", values: ["statistics"] }',
        '                ],',
        '                first: 1,',
        '                order: DESC',
        '            ) {',
        '                edges {',
        '                    node {',
        '                        id',
        '                    }',
        '                }',
        '            }',
        '        }',
        '    `;',
        '    try {',
        '        const response = await fetch("https://uploader.irys.xyz/graphql", {',
        '            method: "POST",',
        '            headers: { "Content-Type": "application/json" },',
        '            body: JSON.stringify({ query })',
        '        });',
        '        const result = await response.json();',
        '        const edges = result.data?.transactions?.edges || [];',
        '        if (edges.length === 0) return null;',
        '        const statsId = edges[0].node.id;',
        '        // Download the statistics data file',
        '        const url = `https://gateway.irys.xyz/${statsId}`;',
        '        const res = await fetch(url);',
        '        const json = await res.json();',
        '        const latestCursor = json.root?.latestCursor || json.latestCursor;',
        '        const totalCount = json.root?.totalCount || json.totalCount || 0;',
        '        return { latestCursor, totalCount };',
        '    } catch (error) {',
        '        console.error("Failed to fetch stats file:", error);',
        '        return null;',
        '    }',
        '}',
        '',
        '// Page from cursor and count new files',
        'async function statFromCursor(startCursor) {',
        '    let cursor = startCursor;',
        '    let pageCount = 0;',
        '    let totalFiles = 0;',
        '    while (pageCount < 50) {',
        '        pageCount++;',
        '        const query = `',
        '            query {',
        '                transactions(',
        '                    tags: [',
        '                        { name: "App-Name", values: ["scivault"] },',
        '                        { name: "Content-Type", values: ["application/pdf"] },',
        '                        { name: "Version", values: ["2.0.0"] }',
        '                    ],',
        '                    first: 1000,',
        '                    order: DESC',
        '                    ${cursor ? `, after: "${cursor}"` : ""}',
        '                ) {',
        '                    edges {',
        '                        node { id }',
        '                        cursor',
        '                    }',
        '                }',
        '            }',
        '        `;',
        '        try {',
        '            const response = await fetch("https://uploader.irys.xyz/graphql", {',
        '                method: "POST",',
        '                headers: { "Content-Type": "application/json" },',
        '                body: JSON.stringify({ query })',
        '            });',
        '            const result = await response.json();',
        '            const edges = result.data?.transactions?.edges || [];',
        '            if (edges.length === 0) break;',
        '            totalFiles += edges.length;',
        '            cursor = edges[edges.length - 1]?.cursor;',
        '            if (!cursor || edges.length < 100) break;',
        '        } catch (err) {',
        '            console.error("Error paging from cursor:", err);',
        '            break;',
        '        }',
        '    }',
        '    return totalFiles;',
        '}',
        '',
        'function updateStatDisplay(total) {',
        '    const totalPapers = document.getElementById("totalPapers");',
        '    const lastUpdated = document.getElementById("lastUpdated");',
        '    if (totalPapers) totalPapers.textContent = total !== null ? total.toLocaleString() : "--";',
        '    if (lastUpdated) {',
        '        const now = new Date();',
        '        lastUpdated.textContent = `Last updated: ${now.toLocaleTimeString()}`;',
        '    }',
        '}',
        '',
        'function showStatError(message) {',
        '    const container = document.querySelector(".stat-container");',
        '    if (container) {',
        '        const errorDiv = document.createElement("div");',
        '        errorDiv.className = "error-message";',
        '        errorDiv.textContent = message;',
        '        container.appendChild(errorDiv);',
        '        setTimeout(() => { errorDiv.remove(); }, 4000);',
        '    }',
        '}',
        '',
        '// Main load function',
        'async function loadStatWidget() {',
        '    try {',
        '        const stats = await fetchStatsFile();',
        '        if (!stats) {',
        '            showStatError("Failed to load stats file");',
        '            return;',
        '        }',
        '        const { latestCursor, totalCount } = stats;',
        '        let newFiles = 0;',
        '        if (latestCursor) {',
        '            newFiles = await statFromCursor(latestCursor);',
        '        }',
        '        const total = totalCount + newFiles;',
        '        updateStatDisplay(total);',
        '    } catch (error) {',
        '        showStatError("Failed to load total papers");',
        '    }',
        '}',
        '',
        'document.addEventListener("DOMContentLoaded", loadStatWidget);',
        'if (document.readyState !== "loading") {',
        '    loadStatWidget();',
        '}'
    ].join('\n')
};

module.exports = { statWidget };
