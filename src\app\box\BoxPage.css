.box-page {
  min-height: 70vh;
  padding: 0;
  padding: 5rem;
  transition: all 0.3s ease;
}

/* Hero Section */
.hero-section1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 90%;
  max-width: 1400px;
  margin: auto;
  border-radius: 2rem;
  transition: all 0.3s ease;
}

.hero-content {}

.hero-title {
  font-size: 4rem !important;
  font-weight: 800 !important;
  margin-bottom: 1rem !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  color: white !important;
}

.hero-subtitle {
  font-size: 1.5rem !important;
  margin-bottom: 0.5rem !important;
  font-weight: 300;
}

.light-theme .hero-subtitle {
  color: #ccc !important;
}

.dark-theme .hero-subtitle {
  color: white !important;
}

.hero-tagline {
  font-size: 1.2rem !important;
  margin-bottom: 2rem !important;
  font-weight: 300;
}

.light-theme .hero-tagline {
  color: #aaa !important;
}

.dark-theme .hero-tagline {
  color: rgba(255, 255, 255, 0.9) !important;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.upload-btn {
  background: linear-gradient(45deg, #ee1d1d, #ee1d1d) !important;
  border: none !important;
  height: 36px !important;
  padding: 0 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 24px !important;
  transition: all 0.3s ease !important;
}

.upload-btn:hover {
  transform: translateY(-2px) !important;
}

.explore-btn {
  height: 48px !important;
  padding: 0 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 24px !important;
  border: 2px solid white !important;
  color: white !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

.explore-btn:hover {
  background: white !important;
  color: #4a90e2 !important;
  transform: translateY(-2px) !important;
}

/* Features Section */
.features-section {
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  width: 90%;
  margin: 2rem auto;
  border-radius: 2rem;
}

.section-title {
  text-align: center !important;
  color: white !important;
  font-size: 2.5rem !important;
  margin-bottom: 3rem !important;
  font-weight: 700 !important;
}

.light-theme .section-title {
  color: #ffffff !important;
}

.dark-theme .section-title {
  color: white !important;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 1rem !important;
  padding: 2rem !important;
  text-align: center !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
}

.feature-card:hover {
  transform: translateY(-8px) !important;
  background: rgba(255, 255, 255, 0.15) !important;
}

.feature-card .ant-card-body {
  padding: 0 !important;
}

.feature-icon-wrapper {
  margin-bottom: 1rem;
}

.feature-card h4 {
  color: white !important;
  font-size: 1.3rem !important;
  margin-bottom: 1rem !important;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.9) !important;
  line-height: 1.6 !important;
}

/* Stats Section */
.stats-section {
  padding: 3rem 2rem;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  width: 90%;
  margin: 2rem auto;
  border-radius: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.stat-item {
  padding: 1rem;
}

.stat-number {
  color: #4a90e2 !important;
  font-size: 3rem !important;
  font-weight: 800 !important;
  margin-bottom: 0.5rem !important;
}

.stat-label {
  color: white !important;
  font-size: 1.1rem !important;
  font-weight: 500 !important;
}

/* Search Section */
.search-section {
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  width: 90%;
  margin: 2rem auto;
  border-radius: 2rem;
}

.search-container {
  max-width: 800px;
  margin: 0 auto;
}

.search-input-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-type-select {
  min-width: 120px;
}

.search-type-select .ant-select-selector {
  border-radius: 1rem !important;
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
}

.main-search-input {
  flex: 1;
  border-radius: 2rem !important;
}

.light-theme .main-search-input .ant-input {
  background: #fff !important;
  color: #333 !important;
}

.light-theme .main-search-input .ant-btn {
  background: #fff !important;
  color: black !important;
}

.dark-theme .main-search-input .ant-btn {
  color: black !important;
}

.dark-theme .main-search-input .ant-input {
  background: #000 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.dark-theme .main-search-input .ant-input::placeholder {
  color: rgba(155, 155, 155, 0.6) !important;
}

/* Papers Section - 主题优化 */
.papers-section1 {
  width: 90%;
  max-width: 1400px;
  margin: 1rem auto;
  border-radius: 1rem;
  transition: all 0.3s ease;
}


.papers-list {
  max-width: 1200px;
  margin: 0 auto;
}

/* 论文列表项 - 主题优化 */
.light-theme .paper-item {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 2px solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 0.75rem !important;
  margin-bottom: 1.5rem !important;
  padding: 1.5rem !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05) !important;
}

.dark-theme .paper-item {
  background: rgba(255, 255, 255, 0.06) !important;
  border: 2px solid rgba(255, 255, 255, 0.12) !important;
  border-radius: 0.75rem !important;
  margin-bottom: 1.5rem !important;
  padding: 1.5rem !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
}

.light-theme .paper-item:hover {
  background: rgba(255, 255, 255, 1) !important;
  border-color: rgba(24, 144, 255, 0.3) !important;
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
}

.dark-theme .paper-item:hover {
  background: rgba(255, 255, 255, 0.12) !important;
  border-color: rgba(64, 169, 255, 0.4) !important;
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4) !important;
}

/* 论文标题 - 主题优化 */
.light-theme .paper-title {
  color: #333 !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin-bottom: 0.5rem !important;
}

.dark-theme .paper-title {
  color: #fff !important;
  font-size: 1.2rem !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin-bottom: 0.5rem !important;
}

.light-theme .paper-title:hover {
  color: #1890ff !important;
}

.dark-theme .paper-title:hover {
  color: #40a9ff !important;
}

/* 论文元数据 - 主题优化 */
.light-theme .paper-meta {
  color: #666 !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
}

.dark-theme .paper-meta {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
}

/* TX ID 标签 - 主题优化 */
.light-theme .tx-id {
  background: rgba(24, 144, 255, 0.1) !important;
  color: #1890ff !important;
  border: 1px solid rgba(24, 144, 255, 0.2) !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.75rem !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
}

.dark-theme .tx-id {
  background: rgba(64, 169, 255, 0.2) !important;
  color: #40a9ff !important;
  border: 1px solid rgba(64, 169, 255, 0.3) !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.75rem !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
}

/* Floating Upload Button */
.floating-upload {
  position: fixed;
  right: 2rem;
  bottom: 2rem;
  z-index: 1000;
}

.floating-upload-btn {
  background: linear-gradient(45deg, #ee1d1d, #ee1d1d) !important;
  border: none !important;
  height: 56px !important;
  padding: 0 1.5rem !important;
  border-radius: 28px !important;
  transition: all 0.3s ease !important;
}

.floating-upload-btn:hover {
  transform: translateY(-4px) !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem !important;
  }

  .hero-subtitle {
    font-size: 1.2rem !important;
  }

  .hero-tagline {
    font-size: 1rem !important;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .upload-btn,
  .explore-btn {
    width: 100%;
    max-width: 1200px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .floating-upload {
    right: 1rem;
    bottom: 1rem;
  }

  .floating-upload-btn {
    width: 56px !important;
    padding: 0 !important;
    border-radius: 50% !important;
  }

  .floating-upload-btn span:not(.anticon) {
    display: none;
  }
}

.search-input-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 80%;
  max-width: 1400px;
  margin: auto;
  transition: all 0.3s ease;
  margin: auto;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 25px 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 现代化Scholar Tabs样式 */
.scholar-tabs {
  position: relative;
}

.scholar-tabs .ant-tabs-nav {
  margin-bottom: 0 !important;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 8px;
}

.scholar-tabs .ant-tabs-tab {
  color: #64748b !important;
  border: none !important;
  border-radius: 12px !important;
  margin: 0 4px !important;
  padding: 12px 20px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: transparent !important;
  position: relative;
  overflow: hidden;
}

.scholar-tabs .ant-tabs-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
  z-index: -1;
}

.scholar-tabs .ant-tabs-tab:hover {
  color: #333 !important;
  background: rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-1px);
}

.scholar-tabs .ant-tabs-tab-active {
  color: #fff !important;
  background: transparent !important;
  font-weight: 600 !important;
  transform: translateY(-1px);
}

.scholar-tabs .ant-tabs-tab-active::before {
  opacity: 1;
}

.scholar-tabs .ant-tabs-ink-bar {
  display: none !important;
}

/* 移除Tabs的所有边框和分割线 */
.scholar-tabs .ant-tabs-nav::before {
  display: none !important;
}

.scholar-tabs .ant-tabs-nav-wrap::before {
  display: none !important;
}

.scholar-tabs .ant-tabs-nav-list::before {
  display: none !important;
}

.scholar-tabs .ant-tabs-content::before {
  display: none !important;
}

.scholar-tabs .ant-tabs-content-holder::before {
  display: none !important;
}

.scholar-tabs .ant-tabs-tab-btn::before {
  display: none !important;
}

/* 移除Tabs导航和内容之间的边框 */
.scholar-tabs .ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap {
  border-bottom: none !important;
}

.scholar-tabs .ant-tabs>.ant-tabs-nav {
  border-bottom: none !important;
}

.scholar-tabs .ant-tabs-nav-wrap {
  border-bottom: none !important;
}

.scholar-tabs .ant-tabs-nav-list {
  border-bottom: none !important;
}

/* 确保内容区域没有顶部边框 */
.scholar-tabs .ant-tabs-content {
  border-top: none !important;
  margin-top: 0 !important;
}

.scholar-tabs .ant-tabs-tabpane {
  border-top: none !important;
}

.scholar-tabs .ant-tabs-content-holder {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-top: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: none;
}

/* 暗色主题 */
.dark-theme .scholar-tabs .ant-tabs-nav {
  background: rgba(30, 30, 30, 0.8);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.dark-theme .scholar-tabs .ant-tabs-tab {
  color: #ccc !important;
}

.dark-theme .scholar-tabs .ant-tabs-tab:hover {
  color: #fff !important;
  background: rgba(255, 255, 255, 0.05) !important;
}

.dark-theme .scholar-tabs .ant-tabs-tab-active {
  color: #fff !important;
}

.dark-theme .scholar-tabs .ant-tabs-content-holder {
  background: rgba(30, 30, 30, 0.6);
  border: none;
}

/* 主容器现代化样式 */
.scholar-container {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(40px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.scholar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.scholar-container:hover {
  transform: translateY(-4px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.dark-theme .scholar-container {
  background: rgba(15, 23, 42, 0.4);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark-theme .scholar-container:hover {
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 现代化卡片样式 */
.modern-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.modern-card:hover::before {
  opacity: 1;
}

.dark-theme .modern-card {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .modern-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  border: 2px dashed rgba(100, 116, 139, 0.3);
  transition: all 0.3s ease;
}

.empty-state:hover {
  border-color: rgba(100, 116, 139, 0.5);
  background: rgba(255, 255, 255, 0.7);
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #666 0%, #333 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark-theme .empty-state {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(148, 163, 184, 0.3);
}

.dark-theme .empty-state:hover {
  border-color: rgba(148, 163, 184, 0.5);
  background: rgba(30, 41, 59, 0.7);
}

/* 现代化按钮样式 */
.modern-btn {
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 8px 20px !important;
  height: auto !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border: none !important;
  position: relative !important;
  overflow: hidden !important;
}

.modern-btn-primary {
  background: linear-gradient(45deg, #ee1d1d, #f71818) !important;

  color: white !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.modern-btn-primary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3) !important;
}

.modern-btn-secondary {
  background: #fff !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
}

.modern-btn-secondary:hover {
  background: #f5f5f5 !important;
  transform: translateY(-1px) !important;
  color: #000 !important;
  border-color: #ccc !important;
}

.modern-btn-danger {
  background: #dc3545 !important;
  color: white !important;
  border: none !important;
}

.modern-btn-danger:hover {
  transform: translateY(-1px) !important;
  background: #c82333 !important;
}



/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 头像样式优化 */
.profile-avatar {
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.profile-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
}

.dark-theme .profile-avatar {
  border-color: rgba(255, 255, 255, 0.2);
}

/* 标题渐变效果 */
.gradient-title {
  background: linear-gradient(135deg, #000 0%, #333 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scholar-container {
    margin: 1rem;
    padding: 1rem;
    border-radius: 16px;
  }

  .scholar-tabs .ant-tabs-nav {
    padding: 6px;
  }

  .scholar-tabs .ant-tabs-tab {
    padding: 8px 12px !important;
    font-size: 13px !important;
  }

  .scholar-tabs .ant-tabs-content-holder {
    padding: 1rem;
  }

  .modern-card {
    padding: 1rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state-icon {
    font-size: 3rem;
  }
}

/* 滚动条美化 */
.scholar-tabs .ant-tabs-content-holder::-webkit-scrollbar {
  width: 6px;
}

.scholar-tabs .ant-tabs-content-holder::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.scholar-tabs .ant-tabs-content-holder::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.scholar-tabs .ant-tabs-content-holder::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}