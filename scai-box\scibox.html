<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SciBox - Web4 Paper Platform</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: system-ui, -apple-system, sans-serif;
            background: #f5f5f5;
        }
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1002;
            color: white;
            font-size: 1.2em;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="loading-overlay">
        <div class="loading-spinner"></div>
        <span>Loading SciBox...</span>
    </div>
    <div id="root">
        <!-- Dynamic content will be loaded here -->
    </div>

    <script>
        const loadingOverlay = document.getElementById('loading-overlay');
        
        class Web4Layout {
            constructor() {
                try {
                    this.init();
                } catch (error) {
                    console.error(error);
                    this.showError('Failed to initialize SciBox');
                }
            }

            async init() {
                try {
                    await this.queryLayout();
                } catch (error) {
                    console.error(error);
                    this.showError('Failed to load layout');
                }
            }

            showError(message) {
                const root = document.getElementById('root');
                if (root) {
                    root.innerHTML = `
                        <div style="
                            padding: 20px;
                            background: #ffebee;
                            color: #c62828;
                            border-radius: 4px;
                            margin: 20px;
                            text-align: center;
                        ">
                            <h2>Error</h2>
                            <p>${message}</p>
                            <button onclick="window.location.reload()">Retry</button>
                        </div>
                    `;
                }
                loadingOverlay.style.display = 'none';
            }

            async queryLayout() {
                const query = `
                    query {
                        transactions(
                            tags: [
                                { name: "Content-Type", values: ["application/json"] },
                                { name: "scinet", values: ["layout"] },
                                { name: "Version", values: ["0.1.9"] }
                            ],
                            first: 1,
                            order: DESC
                        ) {
                            edges {
                                node {
                                    id
                                    tags {
                                        name
                                        value
                                    }
                                }
                            }
                        }
                    }`;

                try {
                    const response = await fetch('https://uploader.irys.xyz/graphql', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.data?.transactions?.edges?.length > 0) {
                        const layoutId = result.data.transactions.edges[0].node.id;
                        const layoutResponse = await fetch(`https://gateway.irys.xyz/${layoutId}`);
                        
                        if (!layoutResponse.ok) {
                            throw new Error(`HTTP error! status: ${layoutResponse.status}`);
                        }

                        const layout = await layoutResponse.json();
                        this.applyLayout(layout);
                    } else {
                        throw new Error('No layout found');
                    }
                } catch (error) {
                    console.error(error);
                    throw error;
                }
            }

            applyLayout(layout) {
                try {
                    const root = document.getElementById('root');
                    if (!root) {
                        throw new Error('Root element not found');
                    }

                    if (layout.html) {
                        root.innerHTML = layout.html;
                    }

                    if (layout.css) {
                        const style = document.createElement('style');
                        style.textContent = layout.css;
                        document.head.appendChild(style);
                    }

                    if (layout.js) {
                        try {
                            const jsCode = layout.js
                                .trim()
                                .replace(/\\n/g, '\n')
                                .replace(/\\r/g, '\r')
                                .replace(/\\t/g, '\t')
                                .replace(/\\\\/g, '\\')
                                .replace(/\\"/g, '"');
                            
                            (function() {
                                try {
                                    eval(jsCode);
                                } catch (evalError) {
                                    console.error('JavaScript execution error:', evalError);
                                }
                            })();
                        } catch (error) {
                            console.error(error);
                            throw error;
                        }
                    }

                    loadingOverlay.style.display = 'none';
                } catch (error) {
                    console.error(error);
                    this.showError('Failed to apply layout');
                }
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new Web4Layout();
        });
    </script>
</body>
</html>