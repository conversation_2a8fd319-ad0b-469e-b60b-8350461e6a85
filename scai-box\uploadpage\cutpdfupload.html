<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SciBox PDF Uploader</title>
    <style>
        :root {
            --bg-dark: #0a0b14;
            --bg-secondary-dark: #12141a;
            --text-primary-dark: #ffffff;
            --text-secondary-dark: #a0aec0;
            --accent-dark: #818cf8;
            --accent-hover-dark: #6366f1;
            --border-dark: rgba(255, 255, 255, 0.05);
            --shadow-dark: rgba(0, 0, 0, 0.3);
            --gradient-start: #4338ca;
            --gradient-mid: #6366f1;
            --gradient-end: #8b5cf6;
        }

        body {
            font-family: 'Space Grotesk', system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--bg-dark);
            color: var(--text-primary-dark);
            line-height: 1.6;
        }

        .pdf-uploader {
            background-color: var(--bg-secondary-dark);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 20px var(--shadow-dark);
            border: 1px solid var(--border-dark);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-mid) 50%, var(--gradient-end) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.05);
            border-left: 4px solid var(--accent-dark);
            color: var(--text-primary-dark);
            font-size: 14px;
        }

        .dropzone {
            border: 2px dashed var(--border-dark);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.02);
        }

        .dropzone.drag-over {
            border-color: var(--accent-dark);
            background: rgba(99, 102, 241, 0.1);
        }

        .dropzone p {
            color: var(--text-secondary-dark);
            margin: 0;
            font-size: 16px;
        }

        .metadata {
            margin-bottom: 30px;
        }

        .metadata input, .metadata textarea {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-dark);
            border-radius: 8px;
            color: var(--text-primary-dark);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .metadata input:focus, .metadata textarea:focus {
            outline: none;
            border-color: var(--accent-dark);
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
        }

        .metadata textarea {
            resize: vertical;
            min-height: 100px;
        }

        .button {
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-mid) 50%, var(--gradient-end) 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
        }

        .button:disabled {
            background: rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress {
            margin-top: 20px;
            display: none;
        }

        .progress-bar {
            height: 8px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--gradient-start) 0%, var(--gradient-mid) 50%, var(--gradient-end) 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 8px;
            color: var(--text-secondary-dark);
            font-size: 14px;
        }

        #uploadResult {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            display: none;
            color: var(--text-primary-dark);
        }

        #uploadResult h3 {
            margin: 0 0 15px 0;
            color: var(--accent-dark);
        }

        #uploadResult a {
            color: var(--accent-dark);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        #uploadResult a:hover {
            color: var(--accent-hover-dark);
        }

        .wallet-selector {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--bg-secondary-dark);
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 20px var(--shadow-dark);
            z-index: 1000;
            min-width: 300px;
            border: 1px solid var(--border-dark);
        }

        .wallet-selector h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            color: var(--text-primary-dark);
            text-align: center;
        }

        .wallet-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin: 8px 0;
            border: 1px solid var(--border-dark);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: rgba(255, 255, 255, 0.02);
        }

        .wallet-option:hover {
            background: rgba(99, 102, 241, 0.1);
            transform: translateY(-2px);
            border-color: var(--accent-dark);
        }

        .wallet-option img {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            border-radius: 8px;
        }

        .wallet-option span {
            font-size: 16px;
            color: var(--text-primary-dark);
            font-weight: 500;
        }

        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 999;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay"></div>
    <div class="wallet-selector" id="walletSelector">
        <h3>Select Wallet</h3>
        <div class="wallet-option" data-wallet="phantom">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiB2aWV3Qm94PSIwIDAgMTA4IDEwOCIgZmlsbD0ibm9uZSI+CjxyZWN0IHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiByeD0iMjYiIGZpbGw9IiNBQjlGRjIiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00Ni41MjY3IDY5LjkyMjlDNDIuNzY4NiA3Mi42ODQ5IDM3Ljk4NzQgNzQuMzg3MiAzMi44NTI5IDc0LjM4NzJDMTkuNDM3MiA3NC4zODcyIDguNTc3NjQgNjMuNTI3NiA4LjU3NzY0IDUwLjExMTlDOC41Nzc2NCAzNi42OTYyIDE5LjQzNzIgMjUuODM2NiAzMi44NTI5IDI1LjgzNjZDMzcuOTg3NCAyNS44MzY2IDQyLjc2ODYgMjcuNTM4OSA0Ni41MjY3IDMwLjMwMDlDNTAuMjg0OCAyNy41Mzg5IDU1LjA2NTkgMjUuODM2NiA2MC4yMDA1IDI1LjgzNjZDNzMuNjE2MSAyNS44MzY2IDg0LjQ3NTcgMzYuNjk2MiA4NC40NzU3IDUwLjExMTlDODQuNDc1NyA2My41Mjc2IDczLjYxNjEgNzQuMzg3MiA2MC4yMDA1IDc0LjM4NzJDNTUuMDY1OSA3NC4zODcyIDUwLjI4NDggNzIuNjg0OSA0Ni41MjY3IDY5LjkyMjlaIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSI2MC4yMDA3IiBjeT0iNTAuMTExOSIgcj0iOC4yMTMyOCIgZmlsbD0iI0FCOUZGMiIvPgo8Y2lyY2xlIGN4PSIzMi44NTI4IiBjeT0iNTAuMTExOSIgcj0iOC4yMTMyOCIgZmlsbD0iI0FCOUZGMiIvPgo8L3N2Zz4K" alt="Phantom">
            <span>Phantom Wallet</span>
        </div>
        <div class="wallet-option" data-wallet="okx">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiByeD0iOCIgZmlsbD0iYmxhY2siLz4KPHBhdGggZD0iTTYgNkgxMFYxMEg2VjZaTTEwIDEwSDE0VjE0SDEwVjEwWk0xNCAxNEgxOFYxOEgxNFYxNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPg==" alt="OKX">
            <span>OKX Wallet</span>
        </div>
    </div>
    <div class="pdf-uploader">
        <div class="header">
            <h1>SciBox PDF Uploader</h1>
        </div>

        <div class="status" id="status">Status: Not connected</div>

        <button class="button" id="connectBtn">Connect Wallet</button>

        <div class="dropzone" id="dropZone">
            <p>Drag and drop your PDF file here or click to select</p>
            <input type="file" id="fileInput" accept=".pdf" style="display: none">
        </div>

        <div class="metadata">
            <input type="text" id="titleInput" placeholder="Enter paper title...">
            <input type="text" id="doiInput" placeholder="Enter DOI...">
            <input type="text" id="authorsInput" placeholder="Enter authors (comma separated)...">
            <textarea id="abstractInput" placeholder="Enter abstract..." rows="4"></textarea>
        </div>

        <button class="button" id="uploadBtn" disabled>Upload PDF</button>

        <div class="progress" id="progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Uploading chunk 0/0</div>
        </div>

        <div id="uploadResult"></div>
    </div>

    <script src="https://uploader.irys.xyz/Cip4wmuMv1K3bmcL4vYoZuV2aQQnnzViqwHm6PCei3QX/bundle.js"></script>
    <script src="https://unpkg.com/@solana/web3.js@latest/lib/index.iife.min.js"></script>
    <script>
        class PDFUploader {
            constructor() {
                this.MAX_CHUNK_SIZE = 50 * 1024; // 50KB per chunk
                this.MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB max file size
                this.RPC_ENDPOINT = 'https://mainnet.helius-rpc.com/?api-key=3ba5d042-cc72-4f2b-b26e-8db8d298aa10';
                this.selectedWallet = null;
                this.initElements();
                this.initEventListeners();
                this.wallet = null;
                this.irys = null;
            }

            initElements() {
                this.elements = {
                    status: document.getElementById('status'),
                    connectBtn: document.getElementById('connectBtn'),
                    uploadBtn: document.getElementById('uploadBtn'),
                    dropZone: document.getElementById('dropZone'),
                    fileInput: document.getElementById('fileInput'),
                    titleInput: document.getElementById('titleInput'),
                    doiInput: document.getElementById('doiInput'),
                    authorsInput: document.getElementById('authorsInput'),
                    abstractInput: document.getElementById('abstractInput'),
                    progress: document.getElementById('progress'),
                    progressFill: document.getElementById('progressFill'),
                    progressText: document.getElementById('progressText'),
                    uploadResult: document.getElementById('uploadResult'),
                    overlay: document.getElementById('overlay'),
                    walletSelector: document.getElementById('walletSelector')
                };
            }

            initEventListeners() {
                this.elements.connectBtn.addEventListener('click', () => this.connectWallet());
                this.elements.uploadBtn.addEventListener('click', () => this.uploadPDF());
                this.elements.dropZone.addEventListener('click', () => this.elements.fileInput.click());
                this.elements.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                this.initDropZone();
                document.querySelectorAll('.wallet-option').forEach(option => {
                    option.addEventListener('click', () => this.selectWallet(option.dataset.wallet));
                });
            }

            initDropZone() {
                this.elements.dropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.elements.dropZone.classList.add('drag-over');
                });

                this.elements.dropZone.addEventListener('dragleave', () => {
                    this.elements.dropZone.classList.remove('drag-over');
                });

                this.elements.dropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.elements.dropZone.classList.remove('drag-over');
                    const file = e.dataTransfer.files[0];
                    if (file) this.handleFile(file);
                });
            }

            showWalletSelector() {
                this.elements.overlay.style.display = 'block';
                this.elements.walletSelector.style.display = 'block';
            }

            hideWalletSelector() {
                this.elements.overlay.style.display = 'none';
                this.elements.walletSelector.style.display = 'none';
            }

            async detectWallets() {
                const availableWallets = [];
                
                if (window.phantom) {
                    availableWallets.push('phantom');
                }
                
                if (window.okxwallet) {
                    availableWallets.push('okx');
                }

                return availableWallets;
            }

            async selectWallet(walletName) {
                this.selectedWallet = walletName;
                this.hideWalletSelector();
                
                try {
                    switch (walletName) {
                        case 'phantom':
                            if (!window.phantom) {
                                throw new Error('Phantom wallet not found');
                            }
                            this.wallet = window.phantom.solana;
                            break;
                        case 'okx':
                            if (!window.okxwallet) {
                                throw new Error('OKX wallet not found');
                            }
                            this.wallet = window.okxwallet.solana;
                            break;
                        default:
                            throw new Error('Unsupported wallet');
                    }

                    await this.connectWallet();
                } catch (error) {
                    console.error('Wallet selection error:', error);
                    this.elements.status.textContent = 'Wallet selection failed: ' + error.message;
                }
            }

            async connectWallet() {
                try {
                    if (!this.wallet) {
                        const availableWallets = await this.detectWallets();
                        
                        if (availableWallets.length === 0) {
                            this.elements.status.textContent = 'Please install a Solana wallet (Phantom or OKX)';
                            return;
                        }
                        
                        if (availableWallets.length === 1) {
                            await this.selectWallet(availableWallets[0]);
                            return;
                        }
                        
                        this.showWalletSelector();
                        return;
                    }

                    await this.wallet.connect();
                    this.elements.status.textContent = `Connected to ${this.selectedWallet}: ${this.wallet.publicKey.toString().slice(0,4)}...${this.wallet.publicKey.toString().slice(-4)}`;
                    this.elements.connectBtn.textContent = 'Connected';
                    await this.initializeIrys();
                } catch (error) {
                    console.error('Wallet connection error:', error);
                    this.elements.status.textContent = 'Connection failed: ' + (error.message || 'Unknown error');
                    this.elements.uploadBtn.disabled = true;
                }
            }

            async initializeIrys() {
                try {
                    const originalSignMessage = window.solana.signMessage;
                    window.solana.signMessage = async (msg) => {
                        const signedMessage = await originalSignMessage.call(window.solana, msg);
                        return signedMessage.signature || signedMessage;
                    };

                    this.irys = await WebIrys.WebUploader(WebIrys.WebSolana).withProvider(window.solana);
                    this.elements.status.textContent = `Ready to upload (${this.irys.address.slice(0,4)}...${this.irys.address.slice(-4)})`;
                    
                    if (this.pdfFile) {
                        this.elements.uploadBtn.disabled = false;
                    }
                } catch (error) {
                    console.error('Irys initialization error:', error);
                    this.elements.status.textContent = 'Initialization failed';
                    this.elements.uploadBtn.disabled = true;
                }
            }

            handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) this.handleFile(file);
            }

            validateMetadata() {
                const title = this.elements.titleInput.value.trim();
                const doi = this.elements.doiInput.value.trim();
                const authors = this.elements.authorsInput.value.trim();
                const abstract = this.elements.abstractInput.value.trim();

                // DOI 格式验证 (只验证基本前缀，允许更灵活的后缀格式)
                const doiRegex = /^10\.\d+\/.+/;
                if (doi && !doiRegex.test(doi)) {
                    alert('Please enter a valid DOI format starting with "10."');
                    return false;
                }

                // 标题长度检查
                if (title && title.length > 500) {
                    alert('Title is too long (max 500 characters)');
                    return false;
                }

                // 作者格式检查
                if (authors) {
                    const authorList = authors.split(',').map(author => author.trim());
                    if (authorList.some(author => author.length > 100)) {
                        alert('Author names are too long (max 100 characters per author)');
                        return false;
                    }
                }

                // 摘要长度检查
                if (abstract && abstract.length > 5000) {
                    alert('Abstract is too long (max 5000 characters)');
                    return false;
                }

                return true;
            }

            validatePDF(file) {
                // 文件类型检查
                if (!file.type.match('application/pdf')) {
                    alert('Please select a PDF file');
                    return false;
                }

                // 文件大小检查
                if (file.size > this.MAX_FILE_SIZE) {
                    alert(`File size exceeds the limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
                    return false;
                }

                // 文件名长度检查
                if (file.name.length > 100) {
                    alert('File name is too long (max 100 characters)');
                    return false;
                }

                return true;
            }

            handleFile(file) {
                if (!this.validatePDF(file)) {
                    return;
                }
                this.pdfFile = file;
                this.elements.uploadBtn.disabled = !this.irys;
                this.elements.dropZone.innerHTML = `<p>Selected: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)}MB)</p>`;
            }

            async retry(fn, { maxAttempts = 3, initialDelay = 1000, factor = 1.5 } = {}) {
                let attempt = 1;
                let delay = initialDelay;

                while (attempt <= maxAttempts) {
                    try {
                        return await fn();
                    } catch (error) {
                        if (attempt === maxAttempts) {
                            throw error;
                        }
                        console.log(`Attempt ${attempt} failed, retrying in ${delay}ms...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        delay *= factor;
                        attempt++;
                    }
                }
            }

            async processPDF(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        try {
                            const arrayBuffer = reader.result;
                            const bytes = new Uint8Array(arrayBuffer);
                            const chunks = [];
                            
                            for (let i = 0; i < bytes.length; i += this.MAX_CHUNK_SIZE) {
                                const chunk = bytes.slice(i, i + this.MAX_CHUNK_SIZE);
                                const base64Chunk = btoa(
                                    chunk.reduce((data, byte) => data + String.fromCharCode(byte), '')
                                );
                                chunks.push(base64Chunk);
                            }
                            resolve(chunks);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(reader.error);
                    reader.readAsArrayBuffer(file);
                });
            }

            async uploadPDF() {
                if (!this.pdfFile || !this.irys) {
                    alert('Please connect to a wallet and select a PDF file');
                    return;
                }

                if (!this.validateMetadata()) {
                    return;
                }
                
                const title = this.elements.titleInput.value.trim();
                const doi = this.elements.doiInput.value.trim();
                const authors = this.elements.authorsInput.value.trim();
                const abstract = this.elements.abstractInput.value.trim();
                
                if (!doi) {
                    alert('Please enter at least the DOI');
                    return;
                }

                try {
                    this.elements.uploadBtn.disabled = true;
                    this.elements.progress.style.display = 'block';
                    
                    if (title || authors || abstract) {
                        this.elements.status.textContent = 'Uploading metadata...';
                        const metadata = {
                            title: title || '',
                            doi: doi,
                            authors: authors || '',
                            abstract: abstract || ''
                        };

                        const metadataTags = [
                            { name: "App-Name", value: "scivault" },
                            { name: "Content-Type", value: "application/json" },
                            { name: "Version", value: "1.0.3" },
                            { name: "doi", value: doi }
                        ];

                        if (title) metadataTags.push({ name: "title", value: title });
                        if (authors) metadataTags.push({ name: "authors", value: authors });

                        await this.retry(async () => {
                            const metadataReceipt = await this.irys.upload(JSON.stringify(metadata), { tags: metadataTags });
                            this.metadataId = metadataReceipt.id;
                        });
                    }

                    this.elements.status.textContent = 'Processing PDF...';
                    const chunks = await this.processPDF(this.pdfFile);
                    
                    this.elements.status.textContent = 'Uploading PDF chunks...';
                    const receiptIds = [];
                    
                    for (let i = 0; i < chunks.length; i++) {
                        this.updateProgress(i + 1, chunks.length);
                        
                        const tags = [
                            { name: "App-Name", value: "scivault" },
                            { name: "Content-Type", value: "application/pdf" },
                            { name: "Version", value: "1.0.3" },
                            { name: "doi", value: doi },
                            { name: "Chunk-Index", value: i.toString() },
                            { name: "Total-Chunks", value: chunks.length.toString() }
                        ];

                        if (title) tags.push({ name: "title", value: title });

                        try {
                            const receipt = await this.retry(async () => {
                                return await this.irys.upload(chunks[i], { tags });
                            });
                            receiptIds.push(receipt.id);
                            console.log(`Successfully uploaded chunk ${i + 1}/${chunks.length}`);
                        } catch (error) {
                            console.error(`Failed to upload chunk ${i + 1}:`, error);
                            throw error;
                        }
                    }
                    
                    this.elements.uploadResult.style.display = 'block';
                    let resultHtml = `
                        <h3>Upload Successful!</h3>
                        <p>PDF Chunks: ${receiptIds.length}</p>
                        <p>First Chunk: <a href="https://gateway.irys.xyz/${receiptIds[0]}" target="_blank">View</a></p>
                    `;
                    
                    if (this.metadataId) {
                        resultHtml += `<p>Metadata ID: ${this.metadataId}</p>`;
                    }
                    
                    this.elements.uploadResult.innerHTML = resultHtml;
                    this.elements.status.textContent = 'Upload complete!';
                } catch (error) {
                    alert('Upload failed: ' + error.message);
                    console.error('Upload error:', error);
                    this.elements.status.textContent = 'Upload failed';
                } finally {
                    this.elements.uploadBtn.disabled = false;
                    this.metadataId = null;
                }
            }

            updateProgress(current, total) {
                const percentage = (current / total) * 100;
                this.elements.progressFill.style.width = `${percentage}%`;
                this.elements.progressText.textContent = `Uploading chunk ${current}/${total}`;
            }
        }

        // Initialize uploader when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new PDFUploader();
        });
    </script>
</body>
</html>
