<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Web4 JSON Component Uploader">
    <title>Web4 JSON Uploader</title>
    <style>
        /* Core styles */
        :root {
            --color-primary: #4a5568;
            --color-primary-hover: #2d3748;
            --color-success: #48bb78;
            --color-success-hover: #38a169;
            --color-border: #e2e8f0;
            --color-background: #f5f5f5;
            --color-white: #ffffff;
            --border-radius: 8px;
            --spacing-unit: 4px;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: var(--color-background);
            line-height: 1.5;
        }

        .web4-uploader {
            background-color: var(--color-white);
            padding: calc(var(--spacing-unit) * 8);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .web4-uploader__header {
            text-align: center;
            margin-bottom: calc(var(--spacing-unit) * 4);
        }

        .web4-uploader__status {
            margin: calc(var(--spacing-unit) * 5) 0;
            padding: calc(var(--spacing-unit) * 4);
            border-radius: var(--border-radius);
            background-color: #edf2f7;
            border-left: 4px solid var(--color-primary);
        }

        .web4-uploader__dropzone {
            border: 2px dashed var(--color-border);
            border-radius: var(--border-radius);
            padding: calc(var(--spacing-unit) * 10);
            text-align: center;
            transition: all 0.3s ease;
            background-color: #f8fafc;
            cursor: pointer;
        }

        .web4-uploader__dropzone.drag-over {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }

        .web4-uploader__editor {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: calc(var(--spacing-unit) * 5);
            margin-top: calc(var(--spacing-unit) * 5);
        }

        .web4-uploader__content {
            width: 100%;
            height: 400px;
            font-family: ui-monospace, monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: calc(var(--spacing-unit) * 2.5);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            resize: vertical;
            background-color: #f8fafc;
        }

        .web4-uploader__tags {
            display: flex;
            flex-direction: column;
            gap: calc(var(--spacing-unit) * 2.5);
        }

        .web4-uploader__tag {
            display: flex;
            gap: calc(var(--spacing-unit) * 2.5);
        }

        .web4-uploader__input {
            flex: 1;
            padding: calc(var(--spacing-unit) * 2);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
        }

        .web4-uploader__button {
            padding: calc(var(--spacing-unit) * 2.5) calc(var(--spacing-unit) * 5);
            cursor: pointer;
            background-color: var(--color-primary);
            color: var(--color-white);
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: background-color 0.2s;
        }

        .web4-uploader__button:hover {
            background-color: var(--color-primary-hover);
        }

        .web4-uploader__button:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
        }

        .web4-uploader__button--upload {
            background-color: var(--color-success);
        }

        .web4-uploader__button--upload:hover {
            background-color: var(--color-success-hover);
        }

        .web4-uploader__result {
            margin-top: calc(var(--spacing-unit) * 5);
            padding: calc(var(--spacing-unit) * 4);
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            display: none;
        }

        .web4-uploader__section {
            margin-top: calc(var(--spacing-unit) * 8);
            padding-top: calc(var(--spacing-unit) * 8);
            border-top: 1px solid var(--color-border);
        }

        .web4-uploader__preview {
            max-width: 100%;
            max-height: 200px;
            margin-top: calc(var(--spacing-unit) * 4);
            display: none;
        }
    </style>
</head>

<body>
    <div class="web4-uploader">
        <header class="web4-uploader__header">
            <h1>Web4 JSON Uploader</h1>
        </header>
        
        <div class="web4-uploader__status" id="status">Status: Not connected</div>
        
        <div class="web4-uploader__actions">
            <button class="web4-uploader__button" id="connectBtn">Connect Wallet</button>
            <button class="web4-uploader__button" id="initIrysBtn" disabled>Initialize Irys</button>
        </div>

        <main class="web4-uploader__main">
            <div class="web4-uploader__dropzone" id="dropZone">
                <p>Drag and drop your JSON file here or click to select</p>
                <input type="file" id="fileInput" accept=".json" style="display: none">
            </div>

            <div class="web4-uploader__editor">
                <textarea 
                    class="web4-uploader__content" 
                    id="fileContent" 
                    disabled 
                    placeholder="JSON content will appear here..."
                ></textarea>
                
                <div class="web4-uploader__tags">
                    <div class="web4-uploader__tag">
                        <input 
                            type="text" 
                            class="web4-uploader__input" 
                            id="contentType" 
                            value="application/json" 
                            readonly
                        >
                    </div>
                    <div class="web4-uploader__tag">
                        <input 
                            type="text" 
                            class="web4-uploader__input" 
                            id="web4Type" 
                            value="layout" 
                            placeholder="Web4 Type"
                        >
                    </div>
                    <div class="web4-uploader__tag">
                        <input 
                            type="text" 
                            class="web4-uploader__input" 
                            id="version" 
                            value="0.1.0" 
                            placeholder="Version"
                        >
                    </div>
                    <button 
                        class="web4-uploader__button web4-uploader__button--upload" 
                        id="uploadBtn" 
                        disabled
                    >Upload JSON</button>
                </div>
            </div>

            <div class="web4-uploader__result" id="uploadResult">
                <h3>Upload Result</h3>
                <pre id="resultContent"></pre>
            </div>

            <!-- Title Image Upload Section -->
            <div class="web4-uploader__section">
                <h2>Title Image Upload</h2>
                <div class="web4-uploader__dropzone" id="imageDropZone">
                    <p>Drag and drop your title image (PNG/JPEG) here or click to select</p>
                    <input type="file" id="imageInput" accept="image/png,image/jpeg" style="display: none">
                    <img id="imagePreview" class="web4-uploader__preview" alt="Title preview">
                </div>
                <div class="web4-uploader__editor">
                    <div class="web4-uploader__tags">
                        <div class="web4-uploader__tag">
                            <input 
                                type="text" 
                                class="web4-uploader__input" 
                                id="imageContentType" 
                                value="image/png" 
                                readonly
                            >
                        </div>
                        <div class="web4-uploader__tag">
                            <input 
                                type="text" 
                                class="web4-uploader__input" 
                                id="imageWeb4Type" 
                                value="title" 
                                placeholder="Web4 Type"
                            >
                        </div>
                        <div class="web4-uploader__tag">
                            <input 
                                type="text" 
                                class="web4-uploader__input" 
                                id="imageVersion" 
                                value="0.1.0" 
                                placeholder="Version"
                            >
                        </div>
                        <button 
                            class="web4-uploader__button web4-uploader__button--upload" 
                            id="uploadImageBtn" 
                            disabled
                        >Upload Title Image</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://uploader.irys.xyz/Cip4wmuMv1K3bmcL4vYoZuV2aQQnnzViqwHm6PCei3QX/bundle.js"></script>
    <script>
        class Web4Uploader {
            constructor() {
                this.initializeElements();
                this.initializeEventListeners();
                this.wallet = null;
                this.irysUploader = null;
            }

            initializeElements() {
                this.elements = {
                    status: document.getElementById('status'),
                    connectBtn: document.getElementById('connectBtn'),
                    initIrysBtn: document.getElementById('initIrysBtn'),
                    uploadBtn: document.getElementById('uploadBtn'),
                    dropZone: document.getElementById('dropZone'),
                    fileInput: document.getElementById('fileInput'),
                    fileContent: document.getElementById('fileContent'),
                    contentType: document.getElementById('contentType'),
                    web4Type: document.getElementById('web4Type'),
                    version: document.getElementById('version'),
                    uploadResult: document.getElementById('uploadResult'),
                    resultContent: document.getElementById('resultContent'),
                    // New elements for image upload
                    imageDropZone: document.getElementById('imageDropZone'),
                    imageInput: document.getElementById('imageInput'),
                    imagePreview: document.getElementById('imagePreview'),
                    uploadImageBtn: document.getElementById('uploadImageBtn'),
                    imageContentType: document.getElementById('imageContentType'),
                    imageWeb4Type: document.getElementById('imageWeb4Type'),
                    imageVersion: document.getElementById('imageVersion')
                };
            }

            initializeEventListeners() {
                // JSON upload events
                this.elements.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
                this.elements.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                this.elements.dropZone.addEventListener('drop', this.handleDrop.bind(this));
                this.elements.dropZone.addEventListener('click', () => this.elements.fileInput.click());
                this.elements.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                
                // Image upload events
                this.elements.imageDropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.elements.imageDropZone.classList.add('drag-over');
                });
                this.elements.imageDropZone.addEventListener('dragleave', () => {
                    this.elements.imageDropZone.classList.remove('drag-over');
                });
                this.elements.imageDropZone.addEventListener('drop', this.handleImageDrop.bind(this));
                this.elements.imageDropZone.addEventListener('click', () => this.elements.imageInput.click());
                this.elements.imageInput.addEventListener('change', this.handleImageSelect.bind(this));
                
                // Wallet and upload events
                this.elements.connectBtn.addEventListener('click', this.connectWallet.bind(this));
                this.elements.initIrysBtn.addEventListener('click', this.initializeIrys.bind(this));
                this.elements.uploadBtn.addEventListener('click', this.uploadFile.bind(this));
                this.elements.uploadImageBtn.addEventListener('click', this.uploadImage.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                this.elements.dropZone.classList.add('drag-over');
            }

            handleDragLeave() {
                this.elements.dropZone.classList.remove('drag-over');
            }

            handleDrop(e) {
                e.preventDefault();
                this.elements.dropZone.classList.remove('drag-over');
                const file = e.dataTransfer.files[0];
                if (file) this.handleFile(file);
            }

            handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) this.handleFile(file);
            }

            handleFile(file) {
                if (!file.name.endsWith('.json')) {
                    alert('Please select a JSON file');
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const content = e.target.result;
                        const jsonObj = JSON.parse(content);
                        this.elements.fileContent.value = JSON.stringify(jsonObj, null, 2);
                    } catch (error) {
                        console.error('Error parsing JSON:', error);
                        alert('Invalid JSON file');
                    }
                };
                reader.readAsText(file);
            }

            handleImageDrop(e) {
                e.preventDefault();
                this.elements.imageDropZone.classList.remove('drag-over');
                const file = e.dataTransfer.files[0];
                if (file) this.handleImageFile(file);
            }

            handleImageSelect(e) {
                const file = e.target.files[0];
                if (file) this.handleImageFile(file);
            }

            handleImageFile(file) {
                if (!file.type.match('image/(png|jpeg)')) {
                    alert('Please select a PNG or JPEG image');
                    return;
                }

                this.elements.imagePreview.src = URL.createObjectURL(file);
                this.elements.imagePreview.style.display = 'block';
                this.elements.uploadImageBtn.disabled = !this.irysUploader;
                this.elements.imageContentType.value = file.type;
                this.imageFile = file;
            }

            async connectWallet() {
                try {
                    if (!window.solana) {
                        this.elements.status.textContent = 'Please install Solana wallet';
                        return;
                    }
                    await window.solana.connect();
                    this.wallet = window.solana;
                    this.elements.status.textContent = `Connected: ${this.wallet.publicKey.toString().slice(0,4)}...${this.wallet.publicKey.toString().slice(-4)}`;
                    this.elements.connectBtn.textContent = 'Connected';
                    this.elements.initIrysBtn.disabled = false;
                } catch (error) {
                    this.elements.status.textContent = 'Connection failed';
                }
            }

            async initializeIrys() {
                try {
                    this.irysUploader = await this.getIrysUploader();
                    this.elements.status.textContent = `Ready to upload (${this.irysUploader.address.slice(0,4)}...${this.irysUploader.address.slice(-4)})`;
                    this.elements.initIrysBtn.textContent = 'Initialized';
                    this.elements.fileContent.disabled = false;
                    this.elements.uploadBtn.disabled = false;
                    if (this.imageFile) this.elements.uploadImageBtn.disabled = false;
                } catch (error) {
                    this.elements.status.textContent = 'Initialization failed';
                }
            }

            async getIrysUploader() {
                if (!this.wallet) throw new Error("Wallet not connected");
                try {
                    const originalSignMessage = window.solana.signMessage;
                    window.solana.signMessage = async (msg) => {
                        const signedMessage = await originalSignMessage.call(window.solana, msg);
                        return signedMessage.signature || signedMessage;
                    };
                    return await WebIrys.WebUploader(WebIrys.WebSolana).withProvider(window.solana);
                } catch (error) {
                    throw new Error("Error connecting to Irys");
                }
            }

            async uploadFile() {
                try {
                    const content = this.elements.fileContent.value.trim();
                    if (!content) {
                        alert('Please load a JSON file first');
                        return;
                    }

                    this.elements.uploadBtn.disabled = true;
                    this.elements.uploadBtn.textContent = 'Uploading...';
                    
                    const receipt = await this.uploadJSON(content);
                    
                    this.elements.uploadResult.style.display = 'block';
                    this.elements.resultContent.textContent = JSON.stringify(receipt, null, 2);
                } catch (error) {
                    alert(error.message);
                } finally {
                    this.elements.uploadBtn.textContent = 'Upload JSON';
                    this.elements.uploadBtn.disabled = false;
                }
            }

            async uploadJSON(content) {
                try {
                    const tags = [
                        { name: "Content-Type", value: this.elements.contentType.value },
                        { name: "scinet", value: this.elements.web4Type.value },
                        { name: "Version", value: this.elements.version.value }
                    ];

                    return await this.irysUploader.upload(content, { tags });
                } catch (error) {
                    throw new Error('Upload failed: ' + error.message);
                }
            }

            async uploadImage() {
                if (!this.imageFile) {
                    alert('Please select an image first');
                    return;
                }

                try {
                    this.elements.uploadImageBtn.disabled = true;
                    this.elements.uploadImageBtn.textContent = 'Uploading...';

                    const tags = [
                        { name: "Content-Type", value: this.elements.imageContentType.value },
                        { name: "Web4", value: this.elements.imageWeb4Type.value },
                        { name: "Version", value: this.elements.imageVersion.value }
                    ];

                    const receipt = await this.irysUploader.uploadFile(this.imageFile, { tags });
                    
                    this.elements.uploadResult.style.display = 'block';
                    this.elements.resultContent.textContent = JSON.stringify(receipt, null, 2);
                } catch (error) {
                    alert('Image upload failed: ' + error.message);
                    console.error('Upload error:', error);
                } finally {
                    this.elements.uploadImageBtn.textContent = 'Upload Title Image';
                    this.elements.uploadImageBtn.disabled = false;
                }
            }
        }

        // Initialize the uploader when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new Web4Uploader();
        });
    </script>
</body>

</html>