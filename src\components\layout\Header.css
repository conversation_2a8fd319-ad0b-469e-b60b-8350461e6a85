.unified-header {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 50;
  background: transparent;
  transition: all 0.3s ease;
}

.unified-header-next {
  background-color: rgba(36, 38, 42, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  margin: 10px auto;
  width: 95%;
  background: blur(32px);
}


.header-nav {
  padding: 0 16px;
  margin: 0 auto;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  position: relative;
}

.header-logo {
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

.logo-image {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 50%;
}

.logo-text {
  font-size: 32px;
  font-weight: 800;
  transition: color 0.2s ease;
}

.light-theme .logo-text {
  color: #fff;
}

.dark-theme .logo-text {
  color: white;
}

.logo-container:hover .logo-text {
  color: #FF3314;
}

.desktop-nav-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: block;
}

.nav-links {
  display: flex;
  align-items: baseline;
  gap: 32px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-link {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 800;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #fff;
  border-radius: 20px;
}

.nav-link:hover {
  color: #FF3314;
}

.light-theme .nav-link:hover {
  background-color: #fff;
  color: #000;
}

.dark-theme .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: #fff;
  color: #000;
}

.light-theme .nav-link.active {
  color: #333;
}

.dark-theme .nav-link.active {
  color: white;
}

.login-btn {
  background-color: #FF3314;
  color: #fff;
  border-radius: 10px;
  padding: 10px 15px;
  font-size: 14px;
  font-weight: 500;
  line-height: 20;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background-color: #fff!important;
  color: #000 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.3) !important;
}

/* .light-theme .login-btn {
  color: #333 !important;
  border: 2px solid rgba(0, 0, 0, 0.4) !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.light-theme .login-btn:hover {
  color: #333 !important;
  background-color: rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(0, 0, 0, 0.6) !important;
}

.dark-theme .login-btn {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.dark-theme .login-btn:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
} */

.theme-toggle-btn {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

.light-theme .theme-toggle-btn {
  color: #333 !important;
  border: 2px solid rgba(0, 0, 0, 0.2) !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

/* .light-theme .theme-toggle-btn:hover {
  color: #333 !important;
  background-color: rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(0, 0, 0, 0.4) !important;
  transform: scale(1.1) !important;
} */

.dark-theme .theme-toggle-btn {
  color: white !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(0, 0, 0, 0.3) !important;
}

/* .dark-theme .theme-toggle-btn:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: scale(1.1) !important;
} */

.mobile-menu-button {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(0, 0, 0, 0.1);
  color: #333;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.mobile-menu-button:hover {
  background-color: rgba(255, 255, 255, 1);
  border-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.menu-icon {
  width: 24px;
  height: 24px;
  font-size: 20px;
  color: #333;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Mobile drawer styles */
.mobile-drawer .ant-drawer-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.mobile-drawer .ant-drawer-header {
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.mobile-drawer .ant-drawer-title {
  color: #333;
  font-weight: 600;
}

.mobile-drawer .ant-drawer-close {
  color: #333;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mobile-drawer .ant-drawer-close:hover {
  background: rgba(255, 77, 79, 0.1);
  color: #FF3314;
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px 16px;
}

.mobile-nav-link {
  color: #333 !important;
  text-align: left;
  padding: 16px 20px;
  border-radius: 12px;
  transition: all 0.2s ease;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.05);
  font-weight: 500;
  font-size: 16px;
}

.mobile-nav-link:hover {
  background-color: rgba(255, 77, 79, 0.1) !important;
  color: #FF3314 !important;
  border-color: rgba(255, 77, 79, 0.2);
  transform: translateX(4px);
}

.mobile-nav-link.active {
  background-color: rgba(255, 77, 79, 0.15) !important;
  color: #FF3314 !important;
  font-weight: 600;
  border-color: rgba(255, 77, 79, 0.3);
}

/* Responsive adjustments */
@media (max-width: 375px) {
  .unified-header {
    width: 95%;
    margin: 5px auto 0;
    padding: 12px 16px;
    /* background-color: rgba(255, 255, 255, 0.9); */
    /* border: 1px solid rgba(0, 0, 0, 0.1); */
    border-radius: 16px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .header-content {
    height: 56px;
    justify-content: space-between;
  }

  .logo-image {
    width: 32px;
    height: 32px;
  }

  .logo-text {
    font-size: 18px;
    color: #333;
  }

  /* .desktop-nav-center,
  .header-right {
    display: none;
  } */

  .mobile-menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
  }
}
