/* Citation Search List Styles */
.citation-search-list {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px;
  min-width: 400px;
  max-width: 500px;
  z-index: 1000;
}

.search-header {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.search-loading .ant-spin {
  margin-right: 8px;
}

.search-loading .ant-typography {
  margin: 0;
}

.citation-item {
  transition: all 0.2s ease;
  border-radius: 6px;
  margin: 2px 0;
}

.citation-item:hover {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
}

.citation-item.selected {
  background-color: #e6f7ff !important;
  border-color: #1890ff !important;
}

.citation-item-content {
  width: 100%;
}

.citation-title {
  display: flex;
  align-items: flex-start;
  margin-bottom: 6px;
  line-height: 1.4;
}

.citation-title .anticon {
  margin-top: 2px;
  flex-shrink: 0;
}

.citation-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.citation-authors,
.citation-year {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.citation-journal {
  margin: 4px 0;
}

.citation-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 6px;
}

.citation-tags .ant-tag {
  margin: 0;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .citation-search-list {
    min-width: 300px;
    max-width: 90vw;
  }

  .citation-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 滚动条样式 */
.citation-search-list .ant-list {
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
}

.citation-search-list .ant-list::-webkit-scrollbar {
  width: 6px;
}

.citation-search-list .ant-list::-webkit-scrollbar-track {
  background: transparent;
}

.citation-search-list .ant-list::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.citation-search-list .ant-list::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 文本截断 */
.citation-title span {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.citation-journal span {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}
