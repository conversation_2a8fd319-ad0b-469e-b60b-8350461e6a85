<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SciBox Paper View</title>
    <script src="https://cdn.jsdelivr.net/npm/pdfobject@2.3.1/pdfobject.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: system-ui, -apple-system, sans-serif;
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <div id="root">
        <!-- Dynamic content will be loaded here -->
    </div>

    <script>
        class PageLayout {
            constructor() {
                this.init();
            }

            async init() {
                await this.queryLayout();
            }

            async queryLayout() {
                const query = `
                    query {
                        transactions(
                            tags: [
                                { name: "Content-Type", values: ["application/json"] },
                                { name: "sciobject", values: ["layout"] },
                                { name: "Version", values: ["0.1.2"] }
                            ],
                            first: 1,
                            order: DESC
                        ) {
                            edges {
                                node {
                                    id
                                    tags {
                                        name
                                        value
                                    }
                                }
                            }
                        }
                    }`;

                try {
                    const response = await fetch('https://uploader.irys.xyz/graphql', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query })
                    });

                    const result = await response.json();
                    if (result.data?.transactions?.edges?.length > 0) {
                        const layoutId = result.data.transactions.edges[0].node.id;
                        const layoutResponse = await fetch(`https://gateway.irys.xyz/${layoutId}`);
                        const layout = await layoutResponse.json();
                        this.applyLayout(layout);
                    }
                } catch (error) {
                    console.error('Error fetching layout:', error);
                }
            }

            applyLayout(layout) {
                const root = document.getElementById('root');
                
                if (layout.html) {
                    root.innerHTML = layout.html;
                }
                
                if (layout.css) {
                    const style = document.createElement('style');
                    style.textContent = layout.css;
                    document.head.appendChild(style);
                }
                
                if (layout.js) {
                    const script = document.createElement('script');
                    script.textContent = layout.js;
                    document.body.appendChild(script);
                }
            }
        }

        // Initialize page layout when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            new PageLayout();
        });
    </script>
</body>
</html>
