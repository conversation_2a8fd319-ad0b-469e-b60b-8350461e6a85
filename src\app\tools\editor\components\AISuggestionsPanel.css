/* AI Suggestions Panel Styles */
.ai-suggestions-panel {
  width: 100%;
  height: 100%;
}

.ai-panel-card {
  height: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-panel-card .ant-card-head {
  background: linear-gradient(135deg, #ee1d1d 0%, #ff4d4f 100%);
  border-bottom: none;
  border-radius: 8px 8px 0 0;
}

.ai-panel-card .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.ai-panel-card .ant-card-extra {
  color: white;
}

.ai-panel-card .ant-card-extra .ant-btn {
  /* color: white; */
  border-color: rgba(255, 255, 255, 0.3);
}
.ai-panel-card .ant-card-extra .ant-btn:disabled {
  color: rgba(255, 255, 255, 0.5) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.ai-panel-card .ant-card-extra .ant-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.ai-panel-card .ant-card-body {
  padding: 16px;
  height: calc(100% - 57px);
  overflow-y: auto;
}

/* Suggestions Content */
.suggestions-content {
  max-height: 300px;
  overflow-y: auto;
}

.suggestions-content .ant-list-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.suggestions-content .ant-list-item:last-child {
  border-bottom: none;
}

.suggestions-content .ant-list-item-action {
  margin-left: 8px;
}

.suggestions-content .ant-list-item-action li {
  padding: 0 2px;
}

/* Outline Display */
.outline-display {
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

.outline-display pre {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  margin: 0;
  font-family: "Courier New", monospace;
  font-size: 11px;
  line-height: 1.4;
}

/* Action Buttons */
.ai-panel-card .ant-btn {
  transition: all 0.3s ease;
}

.ai-panel-card .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(238, 29, 29, 0.2);
}

.ai-panel-card .ant-btn-primary {
  background: linear-gradient(135deg, #ee1d1d 0%, #ff4d4f 100%);
  border-color: #ee1d1d;
}

.ai-panel-card .ant-btn-primary:hover {
  background: linear-gradient(135deg, #d91414 0%, #ff3030 100%);
  border-color: #d91414;
}

/* Modal Styles */
.ant-modal-header {
  background: linear-gradient(135deg, #ee1d1d 0%, #ff4d4f 100%);
  border-radius: 8px 8px 0 0;
}

.ant-modal-title {
  color: white;
  font-weight: 600;
}

.ant-modal-close {
  color: white;
}

.ant-modal-close:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* Form Styles */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #ee1d1d;
  box-shadow: 0 0 0 2px rgba(238, 29, 29, 0.2);
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: #ee1d1d;
}

.ant-select-focused .ant-select-selector {
  border-color: #ee1d1d !important;
  box-shadow: 0 0 0 2px rgba(238, 29, 29, 0.2) !important;
}

/* Alert Styles */
.ant-alert-warning {
  border: 1px solid #faad14;
  background-color: #fffbe6;
}

/* Loading States */
.ai-panel-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-panel-card .ant-card-body {
    padding: 12px;
  }

  .suggestions-content {
    max-height: 200px;
  }

  .outline-display {
    padding: 8px;
  }

  .outline-display pre {
    font-size: 10px;
    padding: 6px;
  }
}

/* Animation Enhancements */
.ai-suggestions-panel .ant-list-item {
  transition: all 0.3s ease;
}

.ai-suggestions-panel .ant-list-item:hover {
  background-color: #f5f5f5;
  transform: translateX(2px);
}

/* Scrollbar Styling */
.suggestions-content::-webkit-scrollbar,
.outline-display > div::-webkit-scrollbar {
  width: 6px;
}

.suggestions-content::-webkit-scrollbar-track,
.outline-display > div::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.suggestions-content::-webkit-scrollbar-thumb,
.outline-display > div::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.suggestions-content::-webkit-scrollbar-thumb:hover,
.outline-display > div::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Tag Styles */
.ai-panel-card .ant-tag {
  border-radius: 12px;
  font-size: 11px;
  padding: 2px 8px;
}

/* Empty State */
.ai-panel-card .ant-empty {
  margin: 16px 0;
}

.ai-panel-card .ant-empty-description {
  color: #8c8c8c;
  font-size: 12px;
}

/* Divider */
.ai-panel-card .ant-divider {
  margin: 12px 0;
  border-color: #e8e8e8;
}

/* Button Groups */
.ai-panel-card .ant-space-item {
  display: flex;
  width: 100%;
}

.ai-panel-card .ant-btn-block {
  height: 32px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* Tooltip Customization */
.ant-tooltip-inner {
  background-color: #262626;
  font-size: 11px;
}

.ant-tooltip-arrow::before {
  background-color: #262626;
}

/* Success States */
.ai-panel-success {
  border-left: 4px solid #52c41a;
  background-color: #f6ffed;
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 4px;
}

/* Error States */
.ai-panel-error {
  border-left: 4px solid #ff4d4f;
  background-color: #fff2f0;
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 4px;
}
