const searchWidget = {
    name: 'Search Widget',
    version: '0.1.9',
    description: 'SciBox 搜索组件',
    
    html: [
        '<div class="search-widget">',
        '    <div class="section-header">',
        '        <h2>Search Papers</h2>',
        '    </div>',
        '    <div class="search-box">',
        '        <div class="search-input-group">',
        '            <select id="searchType">',
        '                <option value="doi">DOI</option>',
        '                <option value="title">Title</option>',
        '                <option value="aid">arXiv ID</option>',
        '            </select>',
        '            <input type="text" id="searchInput" placeholder="Search papers...">',
        '        </div>',
        '        <button onclick="searchPapers()" class="search-button">',
        '            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">',
        '                <circle cx="11" cy="11" r="8"></circle>',
        '                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>',
        '            </svg>',
        '            Search',
        '        </button>',
        '    </div>',
        '    <div id="searchResults" class="search-results"></div>',
        '</div>'
    ].join('\n'),
    
    css: [
        '.search-widget {',
        '    margin-bottom: 30px;',
        '    width: 100%;',
        '}',
        '',
        '.section-header {',
        '    display: flex;',
        '    align-items: center;',
        '    justify-content: space-between;',
        '    margin-bottom: 24px;',
        '}',
        '',
        '.section-header h2 {',
        '    font-size: 1.5em;',
        '    font-weight: 600;',
        '    color: var(--text-primary-dark);',
        '    margin: 0;',
        '}',
        '',
        'body.light-theme .section-header h2 {',
        '    color: var(--text-primary-light);',
        '}',
        '',
        '.search-box {',
        '    display: flex;',
        '    gap: 16px;',
        '    padding: 20px;',
        '    background: var(--bg-secondary-dark);',
        '    border: 1px solid var(--border-dark);',
        '    border-radius: var(--border-radius);',
        '    margin-bottom: 24px;',
        '}',
        '',
        'body.light-theme .search-box {',
        '    background: var(--bg-light);',
        '    border-color: var(--border-light);',
        '}',
        '',
        '.search-input-group {',
        '    display: flex;',
        '    gap: 12px;',
        '    flex: 1;',
        '}',
        '',
        '.search-box select {',
        '    padding: 8px 16px;',
        '    border: 2px solid var(--border-dark);',
        '    border-radius: 8px;',
        '    background: var(--bg-dark);',
        '    color: var(--text-primary-dark);',
        '    min-width: 120px;',
        '    font-size: 14px;',
        '    cursor: pointer;',
        '}',
        '',
        'body.light-theme .search-box select {',
        '    border-color: var(--border-light);',
        '    background: var(--bg-light);',
        '    color: var(--text-primary-light);',
        '}',
        '',
        '.search-box input {',
        '    flex: 1;',
        '    padding: 8px 16px;',
        '    border: 2px solid var(--border-dark);',
        '    border-radius: 8px;',
        '    background: var(--bg-dark);',
        '    color: var(--text-primary-dark);',
        '    font-size: 14px;',
        '}',
        '',
        'body.light-theme .search-box input {',
        '    border-color: var(--border-light);',
        '    background: var(--bg-light);',
        '    color: var(--text-primary-light);',
        '}',
        '',
        '.search-button {',
        '    display: flex;',
        '    align-items: center;',
        '    gap: 8px;',
        '    padding: 8px 20px;',
        '    background: var(--accent-dark);',
        '    color: var(--bg-dark);',
        '    border: none;',
        '    border-radius: 8px;',
        '    font-size: 14px;',
        '    font-weight: 600;',
        '    cursor: pointer;',
        '    transition: all 0.2s;',
        '}',
        '',
        'body.light-theme .search-button {',
        '    background: var(--accent-light);',
        '    color: white;',
        '}',
        '',
        '.search-button:hover {',
        '    transform: translateY(-1px);',
        '    background: var(--accent-hover-dark);',
        '}',
        '',
        'body.light-theme .search-button:hover {',
        '    background: var(--accent-hover-light);',
        '}',
        '',
        '.search-results {',
        '    display: flex;',
        '    flex-direction: column;',
        '    gap: 16px;',
        '}',
        '',
        '.paper-item {',
        '    background: var(--bg-secondary-dark);',
        '    border: 1px solid var(--border-dark);',
        '    border-radius: var(--border-radius);',
        '    padding: 24px;',
        '    transition: all 0.2s;',
        '}',
        '',
        'body.light-theme .paper-item {',
        '    background: var(--bg-light);',
        '    border-color: var(--border-light);',
        '}',
        '',
        '.paper-item:hover {',
        '    transform: translateY(-2px);',
        '    box-shadow: var(--card-shadow-hover);',
        '}',
        '',
        '.paper-item h3 {',
        '    margin: 0 0 12px 0;',
        '    font-size: 1.25em;',
        '    font-weight: 600;',
        '    color: var(--text-primary-dark);',
        '}',
        '',
        'body.light-theme .paper-item h3 {',
        '    color: var(--text-primary-light);',
        '}',
        '',
        '.paper-item h3 a {',
        '    color: inherit;',
        '    text-decoration: none;',
        '}',
        '',
        '.paper-item h3 a:hover {',
        '    color: var(--accent-dark);',
        '}',
        '',
        'body.light-theme .paper-item h3 a:hover {',
        '    color: var(--accent-light);',
        '}',
        '',
        '.paper-meta, .paper-authors, .paper-abstract {',
        '    margin: 8px 0;',
        '    color: var(--text-secondary-dark);',
        '    line-height: 1.6;',
        '}',
        '',
        'body.light-theme .paper-meta,',
        'body.light-theme .paper-authors,',
        'body.light-theme .paper-abstract {',
        '    color: var(--text-secondary-light);',
        '}',
        '',
        '.pdf-links {',
        '    display: flex;',
        '    gap: 12px;',
        '    margin-top: 16px;',
        '}',
        '',
        '.pdf-link {',
        '    display: inline-flex;',
        '    align-items: center;',
        '    gap: 8px;',
        '    padding: 8px 16px;',
        '    background: var(--accent-dark);',
        '    color: var(--bg-dark);',
        '    border: none;',
        '    border-radius: 8px;',
        '    font-size: 14px;',
        '    font-weight: 600;',
        '    text-decoration: none;',
        '    cursor: pointer;',
        '    transition: all 0.2s;',
        '}',
        '',
        'body.light-theme .pdf-link {',
        '    background: var(--accent-light);',
        '    color: white;',
        '}',
        '',
        '.pdf-link:hover {',
        '    transform: translateY(-1px);',
        '    background: var(--accent-hover-dark);',
        '}',
        '',
        'body.light-theme .pdf-link:hover {',
        '    background: var(--accent-hover-light);',
        '}',
        '',
        '@media (max-width: 768px) {',
        '    .search-box {',
        '        flex-direction: column;',
        '        padding: 16px;',
        '    }',
        '',
        '    .search-input-group {',
        '        flex-direction: column;',
        '    }',
        '',
        '    .search-box select,',
        '    .search-box input,',
        '    .search-button {',
        '        width: 100%;',
        '    }',
        '',
        '    .search-button {',
        '        justify-content: center;',
        '    }',
        '',
        '    .pdf-links {',
        '        flex-direction: column;',
        '    }',
        '',
        '    .pdf-link {',
        '        justify-content: center;',
        '    }',
        '}'
    ].join('\n'),
    
    js: [
        'async function executeGraphQLQuery(query) {',
        '    const response = await fetch(\'https://uploader.irys.xyz/graphql\', {',
        '        method: \'POST\',',
        '        headers: { \'Content-Type\': \'application/json\' },',
        '        body: JSON.stringify({ query })',
        '    });',
        '    return response.json();',
        '}',
        '',
        'async function queryPdfVersions(doi) {',
        '    if (!doi) return [];',
        '',
        '    const query1_0_3 = `',
        '        query {',
        '            transactions(',
        '                tags: [',
        '                    { name: "App-Name", values: ["scivault"] },',
        '                    { name: "Content-Type", values: ["application/pdf"] },',
        '                    { name: "Version", values: ["1.0.3"] },',
        '                    { name: "doi", values: ["${doi}"] }',
        '                ]',
        '            ) {',
        '                edges {',
        '                    node {',
        '                        id',
        '                        tags { name value }',
        '                    }',
        '                }',
        '            }',
        '        }',
        '    `;',
        '',
        '    const query2_0_0 = `',
        '        query {',
        '            transactions(',
        '                tags: [',
        '                    { name: "App-Name", values: ["scivault"] },',
        '                    { name: "Content-Type", values: ["application/pdf"] },',
        '                    { name: "Version", values: ["2.0.0"] },',
        '                    { name: "doi", values: ["${doi}"] }',
        '                ]',
        '            ) {',
        '                edges {',
        '                    node {',
        '                        id',
        '                        tags { name value }',
        '                    }',
        '                }',
        '            }',
        '        }',
        '    `;',
        '',
        '    const [result1_0_3, result2_0_0] = await Promise.all([',
        '        executeGraphQLQuery(query1_0_3),',
        '        executeGraphQLQuery(query2_0_0)',
        '    ]);',
        '',
        '    const versions = [];',
        '',
        '    // 处理 1.0.3 版本的切片 PDF',
        '    const edges1_0_3 = result1_0_3.data?.transactions?.edges || [];',
        '    if (edges1_0_3.length > 0) {',
        '        const chunks = edges1_0_3.map(edge => ({',
        '            id: edge.node.id,',
        '            index: parseInt(edge.node.tags.find(tag => tag.name === \'Chunk-Index\')?.value || \'0\')',
        '        })).sort((a, b) => a.index - b.index);',
        '',
        '        if (chunks.length > 0) {',
        '            versions.push({',
        '                version: \'1.0.3\',',
        '                isChunked: true,',
        '                ids: chunks.map(chunk => chunk.id)',
        '            });',
        '        }',
        '    }',
        '',
        '    // 处理 2.0.0 版本的完整 PDF',
        '    const edges2_0_0 = result2_0_0.data?.transactions?.edges || [];',
        '    if (edges2_0_0.length > 0) {',
        '        versions.push({',
        '            version: \'2.0.0\',',
        '            isChunked: false,',
        '            ids: [edges2_0_0[0].node.id]',
        '        });',
        '    }',
        '',
        '    return versions;',
        '}',
        '',
        'async function processPaperMetadata(edge) {',
        '    try {',
        '        const id = edge.node.id;',
        '        const metadataResponse = await fetch(`https://gateway.irys.xyz/${id}`);',
        '        if (!metadataResponse.ok) return null;',
        '        ',
        '        const paper = await metadataResponse.json();',
        '        const doi = edge.node.tags.find(tag => tag.name === \'doi\')?.value;',
        '        ',
        '        if (doi) {',
        '            paper.pdfVersions = await queryPdfVersions(doi);',
        '        } else {',
        '            paper.pdfVersions = [];',
        '        }',
        '        ',
        '        return paper;',
        '    } catch (error) {',
        '        console.error(\'Error processing paper metadata:\', error);',
        '        return null;',
        '    }',
        '}',
        '',
        'async function searchPapers() {',
        '    const searchType = document.getElementById(\'searchType\').value;',
        '    const searchInput = document.getElementById(\'searchInput\').value;',
        '    const resultsDiv = document.getElementById(\'searchResults\');',
        '    ',
        '    if (!searchInput.trim()) {',
        '        resultsDiv.innerHTML = \'<div class="error-message">Please enter a search term</div>\';',
        '        return;',
        '    }',
        '',
        '    resultsDiv.innerHTML = \'<div class="loading">Searching...</div>\';',
        '    ',
        '    try {',
        '        const query = `',
        '            query {',
        '                transactions(',
        '                    tags: [',
        '                        { name: "App-Name", values: ["scivault"] },',
        '                        { name: "Content-Type", values: ["application/json"] },',
        '                        { name: "${searchType}", values: ["${searchInput}"] }',
        '                    ],',
        '                    first: 100',
        '                ) {',
        '                    edges {',
        '                        node {',
        '                            id',
        '                            tags { name value }',
        '                        }',
        '                    }',
        '                }',
        '            }',
        '        `;',
        '',
        '        const result = await executeGraphQLQuery(query);',
        '        const metadataNodes = result.data?.transactions?.edges || [];',
        '        const papers = await Promise.all(metadataNodes.map(edge => processPaperMetadata(edge)));',
        '        const validPapers = papers.filter(paper => paper !== null);',
        '        displayResults(validPapers);',
        '    } catch (error) {',
        '        console.error(\'Error during search:\', error);',
        '        resultsDiv.innerHTML = \'<div class="error-message">Error: Unable to perform search</div>\';',
        '    }',
        '}',
        '',
        'function displayResults(papers) {',
        '    const resultsDiv = document.getElementById(\'searchResults\');',
        '    if (papers.length === 0) {',
        '        resultsDiv.innerHTML = \'<div class="error-message">No papers found</div>\';',
        '        return;',
        '    }',
        '',
        '    resultsDiv.innerHTML = papers.map(paper => {',
        '        const paperUrl = `https://uploader.irys.xyz/7NTozL367vtp2i1REuTKncHvnPjeZTdGMbUxYB4wJGnv?doi=${encodeURIComponent(paper.doi)}`;',
        '        return `',
        '            <div class="paper-item">',
        '                <h3><a href="${paperUrl}" target="_blank">${paper.title || \'Untitled\'}</a></h3>',
        '                <p class="paper-meta">DOI: ${paper.doi || \'N/A\'}</p>',
        '                <p class="paper-authors">${paper.authors || \'Unknown authors\'}</p>',
        '                <p class="paper-abstract">${paper.abstract || \'No abstract available\'}</p>',
        '                ${paper.pdfVersions && paper.pdfVersions.length > 0',
        '                    ? `<div class="pdf-links">',
        '                        ${paper.pdfVersions.map(version => `',
        '                            <a href="https://gateway.irys.xyz/${version.ids[0]}" class="pdf-link" target="_blank">',
        '                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">',
        '                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>',
        '                                    <polyline points="7 10 12 15 17 10"/>',
        '                                    <line x1="12" y1="15" x2="12" y2="3"/>',
        '                                </svg>',
        '                                Download PDF (v${version.version})',
        '                            </a>',
        '                        `).join(\'\')}',
        '                    </div>`',
        '                    : \'<p>PDF Not Available</p>\'',
        '                }',
        '            </div>',
        '        `;',
        '    }).join(\'\');',
        '}'
    ].join('\n')
};

module.exports = { searchWidget }; 