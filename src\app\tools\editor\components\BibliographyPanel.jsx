import React, { useState, useEffect } from "react";
import { Card, List, Button, Select, Typography, Space, Badge, Tooltip, message } from "antd";
import { BookOutlined, CopyOutlined, DownloadOutlined, ReloadOutlined, ExportOutlined, FormatPainterOutlined } from "@ant-design/icons";
import bibliographyService from "../services/BibliographyService";
import "./BibliographyPanel.css";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

/**
 * BibliographyPanel - 参考文献管理面板
 * 显示文档中的所有引用并生成格式化的参考文献列表
 */
const BibliographyPanel = ({ editor, isVisible = true }) => {
  const [bibliography, setBibliography] = useState(null);
  const [format, setFormat] = useState("APA");
  const [loading, setLoading] = useState(false);

  // 刷新参考文献列表
  const refreshBibliography = async () => {
    if (!editor) return;

    setLoading(true);
    try {
      const editorJSON = editor.getJSON();
      const generatedBibliography = bibliographyService.generateBibliography(editorJSON, format);
      setBibliography(generatedBibliography);
    } catch (error) {
      console.error("生成参考文献失败:", error);
      message.error("生成参考文献失败");
    } finally {
      setLoading(false);
    }
  };

  // 监听编辑器内容变化
  useEffect(() => {
    if (!editor) return;

    // 初始生成
    refreshBibliography();

    // 监听编辑器更新事件
    const handleUpdate = () => {
      // 防抖：避免频繁更新
      const timer = setTimeout(() => {
        refreshBibliography();
      }, 1000);

      return () => clearTimeout(timer);
    };

    editor.on("update", handleUpdate);

    return () => {
      editor.off("update", handleUpdate);
    };
  }, [editor, format]);

  // 格式变化时重新生成
  useEffect(() => {
    refreshBibliography();
  }, [format]);

  // 复制参考文献列表
  const copyToClipboard = async () => {
    if (!bibliography) return;

    try {
      const text = bibliographyService.exportAsText(bibliography);
      await navigator.clipboard.writeText(text);
      message.success("参考文献列表已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
      message.error("复制失败");
    }
  };

  // 导出为文本文件
  const exportAsFile = () => {
    if (!bibliography) return;

    const text = bibliographyService.exportAsText(bibliography);
    const blob = new Blob([text], { type: "text/plain;charset=utf-8" });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = `参考文献_${format}_${new Date().toISOString().split("T")[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    message.success("参考文献列表已导出");
  };

  // 插入参考文献到编辑器
  const insertIntoEditor = () => {
    if (!editor || !bibliography) return;

    const html = bibliographyService.exportAsHTML(bibliography);
    editor.chain().focus().insertContent(html).run();
    message.success("参考文献列表已插入到文档中");
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Card
      className="bibliography-panel"
      title={
        <Space>
          <BookOutlined />
          <span>参考文献</span>
          <Badge count={bibliography?.count || 0} style={{ backgroundColor: "#52c41a" }} />
        </Space>
      }
      size="small"
      extra={
        <Space>
          <Select value={format} onChange={setFormat} size="small" style={{ width: 80 }}>
            <Option value="APA">APA</Option>
            <Option value="MLA">MLA</Option>
          </Select>
          <Tooltip title="刷新">
            <Button type="text" size="small" icon={<ReloadOutlined />} onClick={refreshBibliography} loading={loading} />
          </Tooltip>
        </Space>
      }
    >
      <div className="bibliography-content">
        {/* 操作按钮 */}
        <div className="bibliography-actions">
          <Space wrap>
            <Button size="small" icon={<CopyOutlined />} onClick={copyToClipboard} disabled={!bibliography || bibliography.count === 0}>
              复制
            </Button>
            <Button size="small" icon={<DownloadOutlined />} onClick={exportAsFile} disabled={!bibliography || bibliography.count === 0}>
              导出
            </Button>
            <Button size="small" icon={<ExportOutlined />} onClick={insertIntoEditor} disabled={!bibliography || bibliography.count === 0}>
              插入文档
            </Button>
          </Space>
        </div>

        {/* 引用列表 */}
        {bibliography && bibliography.count > 0 ? (
          <List
            className="bibliography-list"
            size="small"
            dataSource={bibliography.citations}
            renderItem={(citation) => (
              <List.Item key={citation.id} className="bibliography-item">
                <div className="citation-content">
                  <Text className="citation-number">{citation.index}.</Text>
                  <Paragraph
                    className="citation-text"
                    ellipsis={{
                      rows: 3,
                      expandable: true,
                      symbol: "展开",
                    }}
                  >
                    {citation.formatted}
                  </Paragraph>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <div className="bibliography-empty">
            <FormatPainterOutlined style={{ fontSize: 32, color: "#ccc", marginBottom: 8 }} />
            <Text type="secondary">文档中暂无引用文献</Text>
            <br />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              使用 [ 搜索并添加文献引用
            </Text>
          </div>
        )}

        {/* 统计信息 */}
        {bibliography && (
          <div className="bibliography-stats">
            <Text type="secondary" style={{ fontSize: "11px" }}>
              格式: {bibliography.format} | 引用数: {bibliography.count} | 更新时间: {new Date(bibliography.generated).toLocaleTimeString()}
            </Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default BibliographyPanel;
